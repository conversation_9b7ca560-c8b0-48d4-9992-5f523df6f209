/**
 * TürkçeDüzelt - Turkish Grammar Correction Assistant
 * Grammar checking functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const checkGrammarBtn = document.getElementById('check-grammar');
    const errorCountElement = document.getElementById('error-count');
    const grammarTab = document.getElementById('grammar-tab');
    const spellingTab = document.getElementById('spelling-tab');
    const styleTab = document.getElementById('style-tab');
    const tabButtons = document.querySelectorAll('.tab-button');
    const modal = document.getElementById('error-modal');
    const closeModal = document.querySelector('.close');
    const applySuggestionBtn = document.querySelector('.apply-suggestion');

    // Current error being displayed in the modal
    let currentError = null;

    // Initialize
    init();

    function init() {
        // Check grammar button
        checkGrammarBtn.addEventListener('click', checkGrammar);

        // Tab switching
        tabButtons.forEach(button => {
            button.addEventListener('click', function() {
                const tabName = this.getAttribute('data-tab');
                switchTab(tabName);
            });
        });

        // Modal close button
        closeModal.addEventListener('click', function() {
            modal.style.display = 'none';
        });

        // Close modal when clicking outside
        window.addEventListener('click', function(e) {
            if (e.target === modal) {
                modal.style.display = 'none';
            }
        });

        // Apply suggestion button
        applySuggestionBtn.addEventListener('click', applySelectedSuggestion);
    }

    /**
     * Switch between correction tabs (grammar, spelling, style)
     */
    function switchTab(tabName) {
        // Hide all tabs
        document.querySelectorAll('.tab-content').forEach(tab => {
            tab.style.display = 'none';
        });

        // Remove active class from all buttons
        tabButtons.forEach(button => {
            button.classList.remove('active');
        });

        // Show selected tab
        document.getElementById(tabName + '-tab').style.display = 'block';

        // Set active class on selected button
        document.querySelector(`.tab-button[data-tab="${tabName}"]`).classList.add('active');
    }

    /**
     * Check grammar in the editor content
     */
    async function checkGrammar() {
        // Get editor content
        const content = window.editorModule.getContent();

        // Get selected writing mode
        const writingMode = document.getElementById('writing-mode').value;

        // Remove existing highlights
        window.editorModule.removeHighlights();

        // Clear error lists
        clearErrorLists();

        // Show loading indicator
        document.getElementById('check-grammar').innerHTML = '<i class="fas fa-spinner fa-spin"></i> Checking...';

        try {
            // Perform grammar check using API
            const errors = await checkTextWithAPI(content, writingMode);

            // Update error count
            errorCountElement.textContent = errors.length;

            // Display errors in the correction panel
            displayErrors(errors);

            // Highlight errors in the text
            highlightErrors(errors);
        } catch (error) {
            console.error('Error checking grammar:', error);
            alert('Dilbilgisi kontrolü sırasında bir hata oluştu. Lütfen tekrar deneyin.');

            // Fallback to client-side checking
            const errors = analyzeText(content);
            errorCountElement.textContent = errors.length;
            displayErrors(errors);
            highlightErrors(errors);
        } finally {
            // Reset button text
            document.getElementById('check-grammar').innerHTML = '<i class="fas fa-check"></i> Check Grammar';
        }
    }

    /**
     * Clear error lists in all tabs
     */
    function clearErrorLists() {
        document.querySelectorAll('.error-list').forEach(list => {
            list.innerHTML = '';
        });

        document.querySelectorAll('.no-errors').forEach(message => {
            message.style.display = 'block';
        });
    }

    /**
     * Display errors in the correction panel
     */
    function displayErrors(errors) {
        // Group errors by type
        const grammarErrors = errors.filter(error => error.type === 'grammar');
        const spellingErrors = errors.filter(error => error.type === 'spelling');
        const styleErrors = errors.filter(error => error.type === 'style');

        // Update grammar tab
        updateErrorTab(grammarTab, grammarErrors, 'grammar');

        // Update spelling tab
        updateErrorTab(spellingTab, spellingErrors, 'spelling');

        // Update style tab
        updateErrorTab(styleTab, styleErrors, 'style');
    }

    /**
     * Update a specific error tab with errors
     */
    function updateErrorTab(tabElement, errors, errorType) {
        const errorList = tabElement.querySelector('.error-list');
        const noErrorsMessage = tabElement.querySelector('.no-errors');

        if (errors.length === 0) {
            noErrorsMessage.style.display = 'block';
            return;
        }

        noErrorsMessage.style.display = 'none';

        errors.forEach(error => {
            const errorItem = document.createElement('div');
            errorItem.className = `error-item ${errorType}`;
            errorItem.dataset.errorId = error.id;

            errorItem.innerHTML = `
                <h4>${error.title}</h4>
                <p>${error.context}</p>
            `;

            errorItem.addEventListener('click', function() {
                showErrorDetails(error);
            });

            errorList.appendChild(errorItem);
        });
    }

    /**
     * Highlight errors in the editor
     */
    function highlightErrors(errors) {
        // This is a simplified implementation
        // In a real application, we would need to find the exact nodes and offsets

        const editor = document.getElementById('editor');

        errors.forEach(error => {
            // Find the text node containing the error
            const textNodes = getAllTextNodes(editor);

            for (const node of textNodes) {
                const text = node.nodeValue;
                const index = text.indexOf(error.text);

                if (index !== -1) {
                    // Create a range around the error text
                    const range = document.createRange();
                    range.setStart(node, index);
                    range.setEnd(node, index + error.text.length);

                    // Create a span to highlight the error
                    const span = document.createElement('span');
                    span.className = `${error.type}-error`;
                    span.dataset.errorId = error.id;

                    // Wrap the error text in the span
                    range.surroundContents(span);

                    // Add click event to show error details
                    span.addEventListener('click', function() {
                        showErrorDetails(error);
                    });

                    // Since we've modified the DOM, we need to break out of the loop
                    break;
                }
            }
        });
    }

    /**
     * Get all text nodes in an element
     */
    function getAllTextNodes(element) {
        const textNodes = [];
        const walker = document.createTreeWalker(
            element,
            NodeFilter.SHOW_TEXT,
            null,
            false
        );

        let node;
        while (node = walker.nextNode()) {
            textNodes.push(node);
        }

        return textNodes;
    }

    /**
     * Show error details in the modal
     */
    function showErrorDetails(error) {
        // Set current error
        currentError = error;

        // Update modal content
        document.querySelector('.error-text').textContent = error.context;
        document.querySelector('.explanation-text').textContent = error.explanation;

        // Clear suggestions list
        const suggestionsList = document.querySelector('.suggestions-list');
        suggestionsList.innerHTML = '';

        // Add suggestions
        error.suggestions.forEach((suggestion, index) => {
            const li = document.createElement('li');
            li.textContent = suggestion;
            li.dataset.index = index;

            li.addEventListener('click', function() {
                // Remove selected class from all suggestions
                suggestionsList.querySelectorAll('li').forEach(item => {
                    item.classList.remove('selected');
                });

                // Add selected class to clicked suggestion
                this.classList.add('selected');
            });

            suggestionsList.appendChild(li);
        });

        // Select first suggestion by default
        if (suggestionsList.firstChild) {
            suggestionsList.firstChild.classList.add('selected');
        }

        // Show modal
        modal.style.display = 'block';
    }

    /**
     * Apply the selected suggestion
     */
    function applySelectedSuggestion() {
        if (!currentError) return;

        const selectedSuggestion = document.querySelector('.suggestions-list li.selected');
        if (!selectedSuggestion) return;

        const suggestionText = selectedSuggestion.textContent;

        // Find the error element in the editor
        const errorElement = document.querySelector(`[data-error-id="${currentError.id}"]`);
        if (!errorElement) return;

        // Replace the error text with the suggestion
        errorElement.textContent = suggestionText;

        // Remove the error highlighting but keep the text
        const parent = errorElement.parentNode;
        while (errorElement.firstChild) {
            parent.insertBefore(errorElement.firstChild, errorElement);
        }
        parent.removeChild(errorElement);

        // Close the modal
        modal.style.display = 'none';

        // Remove the error from the correction panel
        document.querySelector(`.error-item[data-error-id="${currentError.id}"]`).remove();

        // Update error count
        const errorCount = parseInt(errorCountElement.textContent) - 1;
        errorCountElement.textContent = errorCount;

        // Check if there are no more errors of this type
        const errorType = currentError.type;
        const errorList = document.querySelector(`#${errorType}-tab .error-list`);

        if (errorList.children.length === 0) {
            document.querySelector(`#${errorType}-tab .no-errors`).style.display = 'block';
        }

        // Reset current error
        currentError = null;
    }

    /**
     * Analyze text for grammar, spelling, and style errors
     * This is a simplified implementation with some common Turkish grammar rules
     */
    function analyzeText(html) {
        // Extract text from HTML
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = html;
        const text = tempDiv.innerText;

        // Array to store errors
        const errors = [];

        // Split text into sentences
        const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);

        // Check each sentence
        sentences.forEach((sentence, sentenceIndex) => {
            // Check for common Turkish grammar errors
            checkTurkishGrammarRules(sentence, sentenceIndex, errors);

            // Check for spelling errors
            checkTurkishSpelling(sentence, sentenceIndex, errors);

            // Check for style suggestions
            checkTurkishStyle(sentence, sentenceIndex, errors);
        });

        return errors;
    }

    /**
     * Check for common Turkish grammar rules
     */
    function checkTurkishGrammarRules(sentence, sentenceIndex, errors) {
        // Example rule: Vowel harmony
        checkVowelHarmony(sentence, sentenceIndex, errors);

        // Example rule: Subject-verb agreement
        checkSubjectVerbAgreement(sentence, sentenceIndex, errors);

        // Example rule: Proper use of suffixes
        checkSuffixUsage(sentence, sentenceIndex, errors);
    }

    /**
     * Check for vowel harmony in Turkish words
     * This is a simplified implementation
     */
    function checkVowelHarmony(sentence, sentenceIndex, errors) {
        // Example: "kitabda" should be "kitapta" (consonant harmony)
        if (sentence.includes('kitabda')) {
            errors.push({
                id: `grammar-${sentenceIndex}-1`,
                type: 'grammar',
                title: 'Ünsüz Uyumu Hatası',
                text: 'kitabda',
                context: `"...${sentence.trim()}..."`,
                explanation: 'Türkçede ünsüz uyumu kuralına göre, sert ünsüzlerden sonra sert ünsüzler, yumuşak ünsüzlerden sonra yumuşak ünsüzler gelir.',
                suggestions: ['kitapta']
            });
        }

        // Example: "geliyorum" is correct, but "geliyorım" would violate vowel harmony
        if (sentence.includes('geliyorım')) {
            errors.push({
                id: `grammar-${sentenceIndex}-2`,
                type: 'grammar',
                title: 'Büyük Ünlü Uyumu Hatası',
                text: 'geliyorım',
                context: `"...${sentence.trim()}..."`,
                explanation: 'Türkçede büyük ünlü uyumu kuralına göre, bir sözcükte ya hep kalın ünlüler (a, ı, o, u) ya da hep ince ünlüler (e, i, ö, ü) bulunur.',
                suggestions: ['geliyorum']
            });
        }
    }

    /**
     * Check for subject-verb agreement in Turkish
     * This is a simplified implementation
     */
    function checkSubjectVerbAgreement(sentence, sentenceIndex, errors) {
        // Example: "Ben geldi" should be "Ben geldim"
        if (sentence.includes('ben geldi')) {
            errors.push({
                id: `grammar-${sentenceIndex}-3`,
                type: 'grammar',
                title: 'Özne-Yüklem Uyumsuzluğu',
                text: 'ben geldi',
                context: `"...${sentence.trim()}..."`,
                explanation: 'Türkçede özne ile yüklem arasında kişi bakımından uyum olmalıdır. "Ben" birinci tekil şahıs olduğu için yüklem de birinci tekil şahıs olmalıdır.',
                suggestions: ['ben geldim']
            });
        }

        // Example: "Biz gidiyorsun" should be "Biz gidiyoruz"
        if (sentence.includes('biz gidiyorsun')) {
            errors.push({
                id: `grammar-${sentenceIndex}-4`,
                type: 'grammar',
                title: 'Özne-Yüklem Uyumsuzluğu',
                text: 'biz gidiyorsun',
                context: `"...${sentence.trim()}..."`,
                explanation: 'Türkçede özne ile yüklem arasında kişi bakımından uyum olmalıdır. "Biz" birinci çoğul şahıs olduğu için yüklem de birinci çoğul şahıs olmalıdır.',
                suggestions: ['biz gidiyoruz']
            });
        }
    }

    /**
     * Check for proper use of suffixes in Turkish
     * This is a simplified implementation
     */
    function checkSuffixUsage(sentence, sentenceIndex, errors) {
        // Example: "evde" is correct, but "evda" would be incorrect
        if (sentence.includes('evda')) {
            errors.push({
                id: `grammar-${sentenceIndex}-5`,
                type: 'grammar',
                title: 'Ek Kullanım Hatası',
                text: 'evda',
                context: `"...${sentence.trim()}..."`,
                explanation: 'Türkçede küçük ünlü uyumu kuralına göre, son hecesinde ince ünlü (e, i, ö, ü) bulunan sözcüklere ince ünlülü ekler (-e, -i, -de, -den) gelir.',
                suggestions: ['evde']
            });
        }

        // Example: "arabayı" is correct, but "arabai" would be incorrect
        if (sentence.includes('arabai')) {
            errors.push({
                id: `grammar-${sentenceIndex}-6`,
                type: 'grammar',
                title: 'Ek Kullanım Hatası',
                text: 'arabai',
                context: `"...${sentence.trim()}..."`,
                explanation: 'Türkçede belirtme hali eki, son ses ünlü ise "-yı, -yi, -yu, -yü", ünsüz ise "-ı, -i, -u, -ü" şeklinde gelir.',
                suggestions: ['arabayı']
            });
        }
    }

    /**
     * Check for spelling errors in Turkish
     * This is a simplified implementation
     */
    function checkTurkishSpelling(sentence, sentenceIndex, errors) {
        // Example misspellings
        const misspellings = [
            { wrong: 'yalnış', correct: 'yanlış' },
            { wrong: 'herşey', correct: 'her şey' },
            { wrong: 'birşey', correct: 'bir şey' },
            { wrong: 'yada', correct: 'ya da' },
            { wrong: 'herzaman', correct: 'her zaman' },
            { wrong: 'hiçbirşey', correct: 'hiçbir şey' }
        ];

        misspellings.forEach((item, index) => {
            if (sentence.includes(item.wrong)) {
                errors.push({
                    id: `spelling-${sentenceIndex}-${index}`,
                    type: 'spelling',
                    title: 'Yazım Hatası',
                    text: item.wrong,
                    context: `"...${sentence.trim()}..."`,
                    explanation: `"${item.wrong}" sözcüğü yanlış yazılmıştır.`,
                    suggestions: [item.correct]
                });
            }
        });
    }

    /**
     * Check for style suggestions in Turkish
     * This is a simplified implementation
     */
    function checkTurkishStyle(sentence, sentenceIndex, errors) {
        // Example: Redundant words
        if (sentence.includes('en optimum')) {
            errors.push({
                id: `style-${sentenceIndex}-1`,
                type: 'style',
                title: 'Gereksiz Sözcük Kullanımı',
                text: 'en optimum',
                context: `"...${sentence.trim()}..."`,
                explanation: '"Optimum" zaten "en uygun" anlamına geldiği için "en" sözcüğü gereksizdir.',
                suggestions: ['optimum']
            });
        }

        // Example: Informal expressions in formal context
        const writingMode = document.getElementById('writing-mode').value;

        if (writingMode === 'formal' || writingMode === 'academic') {
            // Check for informal expressions
            const informalExpressions = [
                { text: 'falan filan', suggestions: ['ve benzeri', 'vb.', 'gibi'] },
                { text: 'yani', suggestions: ['başka bir deyişle', 'diğer bir ifadeyle'] },
                { text: 'bence', suggestions: ['düşünceme göre', 'kanımca'] }
            ];

            informalExpressions.forEach((item, index) => {
                if (sentence.includes(item.text)) {
                    errors.push({
                        id: `style-${sentenceIndex}-${index + 2}`,
                        type: 'style',
                        title: 'Resmi Dil Kullanımı',
                        text: item.text,
                        context: `"...${sentence.trim()}..."`,
                        explanation: `"${item.text}" ifadesi resmi/akademik yazım için çok gündelik bir ifadedir.`,
                        suggestions: item.suggestions
                    });
                }
            });
        }
    }

    // Expose checkGrammar for real-time use
    window.grammarCheckerModule = {
        checkGrammar,
        showErrorDetails
    };
});
