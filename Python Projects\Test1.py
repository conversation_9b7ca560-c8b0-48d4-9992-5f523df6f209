# info stuff: [phone, model, OS, Edition]
stuff = { 'Phones': ['Google Pixel', 'Iphone', 'Samsung', 'Huawei'], 'Edition': [6, 13, 'Galaxy S22', 'P50'], 'OS': ['Android 12', 'IOS 15', 'Android 12', 'Android 12'] }
with open('dict_string', 'a+') as f:
    for key, value in stuff.items():
              f.write(f'{key}:{value}\n')      
              print(f'{key}:{value}\n')

def string_dict():
    item = input('Enter any items to split key and value by: ').split(':')
    if len(item) != 2 or not item[0] or not item[1]:
        print('Enter the data correctly')
        return string_dict()
    dict_string = {item[0]: item[1]}
    print('Dictionary:', dict_string)


# Calling function
string_dict()

with open('dict_string') as f:
    data = dict()
    for i in f.readlines():
        if i != '\n':
            item = i.split(':')
            data[item[0]] = item[1].strip()
            print(data)
