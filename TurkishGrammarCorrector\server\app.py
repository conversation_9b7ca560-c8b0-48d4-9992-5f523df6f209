from flask import Flask, request, jsonify # type: ignore
from flask_cors import CORS # type: ignore
import os # type: ignore
from werkzeug.utils import secure_filename # type: ignore

# Import our custom modules
from models.grammar_checker import <PERSON><PERSON><PERSON><PERSON>
from models.spell_checker import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from models.style_enhancer import StyleEnhancer
from utils.file_handler import <PERSON>Handler
from utils.text_processor import TextProcessor

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Initialize our NLP components
grammar_checker = GrammarChecker()
spell_checker = SpellChecker()
style_enhancer = StyleEnhancer()
file_handler = FileHandler()
text_processor = TextProcessor()

UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'txt', 'docx'}

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max upload size

# Create uploads folder if it doesn't exist
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

# --- Hugging Face Transformers Integration (optional, advanced Turkish NLP) ---
USE_TURKISH_NLP_MODEL = True
try:
    if USE_TURKISH_NLP_MODEL:
        from transformers import AutoTokenizer, AutoModelForTokenClassification, pipeline # type: ignore
        # You can change the model to another Turkish model if desired
        TURKISH_MODEL_NAME = "savasy/bert-base-turkish-uncased"
        tokenizer = AutoTokenizer.from_pretrained(TURKISH_MODEL_NAME)
        model = AutoModelForTokenClassification.from_pretrained(TURKISH_MODEL_NAME)
        nlp_pipeline = pipeline("ner", model=model, tokenizer=tokenizer)
    else:
        nlp_pipeline = None
except Exception as e:
    print(f"[INFO] Hugging Face Turkish NLP model could not be loaded: {e}")
    nlp_pipeline = None

def analyze_with_turkish_nlp(text):
    """
    Use Hugging Face Turkish model to analyze text for advanced suggestions.
    Returns a dict with possible grammar, spelling, and style issues.
    """
    if not nlp_pipeline:
        return None
    # Example: Use NER to find named entities, can be extended for grammar/style
    results = nlp_pipeline(text)
    # --- ADVANCED: Add grammar and spelling suggestions using model output ---
    grammar_issues = []
    spelling_issues = []
    style_suggestions = []
    for ent in results:
        # Named entity as style suggestion
        if ent['entity'].startswith('B-') or ent['entity'].startswith('I-'):
            style_suggestions.append({
                'text': text[ent['start']:ent['end']],
                'explanation': f"Metinde özel isim tespit edildi: {ent['entity']}",
                'suggestion': text[ent['start']:ent['end']].title(),
                'subtype': 'Özel İsim'
            })
        # Example: flag all-uppercase words as possible style/grammar issues
        if text[ent['start']:ent['end']].isupper() and len(text[ent['start']:ent['end']]) > 2:
            grammar_issues.append({
                'text': text[ent['start']:ent['end']],
                'explanation': 'Tüm harfler büyük yazılmış. Türkçede yalnızca özel isimler veya kısaltmalar büyük harfle yazılır.',
                'suggestion': text[ent['start']:ent['end']].capitalize(),
                'subtype': 'Büyük Harf Kullanımı'
            })
    # --- You can add more advanced logic here (e.g., use a sequence classification model for grammar) ---
    return {
        'grammar_issues': grammar_issues,
        'spelling_issues': spelling_issues,
        'style_suggestions': style_suggestions
    }

@app.route('/api/check', methods=['POST'])
def check_text():
    """
    Endpoint to check text for grammar, spelling, and style issues
    """
    data = request.json
    if not data or 'text' not in data:
        return jsonify({'error': 'No text provided'}), 400
    
    text = data['text']
    mode = data.get('mode', 'standard')  # standard, formal, academic, creative
    
    # Process the text
    processed_text = text_processor.preprocess(text)
    
    # If advanced NLP is enabled and available, use it for deeper analysis
    nlp_results = analyze_with_turkish_nlp(processed_text) if nlp_pipeline else None
    if nlp_results:
        # Merge results: combine rule-based and model-based suggestions
        grammar_issues = grammar_checker.check(processed_text) + nlp_results['grammar_issues']
        spelling_issues = spell_checker.check(processed_text) + nlp_results['spelling_issues']
        style_suggestions = style_enhancer.enhance(processed_text, mode) + nlp_results['style_suggestions']
    else:
        grammar_issues = grammar_checker.check(processed_text)
        spelling_issues = spell_checker.check(processed_text)
        style_suggestions = style_enhancer.enhance(processed_text, mode)
    
    return jsonify({
        'grammar_issues': grammar_issues,
        'spelling_issues': spelling_issues,
        'style_suggestions': style_suggestions
    })

@app.route('/api/upload', methods=['POST'])
def upload_file():
    """
    Endpoint to upload and process document files
    """
    if 'file' not in request.files:
        return jsonify({'error': 'No file part'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No selected file'}), 400
    
    if file and allowed_file(file.filename):
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)
        
        # Extract text from file
        text = file_handler.extract_text(filepath)
        
        # Process the extracted text
        return check_text_from_file(text)
    
    return jsonify({'error': 'File type not allowed'}), 400

def check_text_from_file(text):
    """
    Process text extracted from uploaded files
    """
    # Process the text
    processed_text = text_processor.preprocess(text)
    
    # Check grammar
    grammar_issues = grammar_checker.check(processed_text)
    
    # Check spelling
    spelling_issues = spell_checker.check(processed_text)
    
    # Get style suggestions (using standard mode for files)
    style_suggestions = style_enhancer.enhance(processed_text, 'standard')
    
    return jsonify({
        'text': text,
        'grammar_issues': grammar_issues,
        'spelling_issues': spelling_issues,
        'style_suggestions': style_suggestions
    })

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
