<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Türkçe Dilbilgisi ve Stil Kontrolü</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f7fafc;
        }
        .editor {
            min-height: 300px;
            border: 1px solid #e2e8f0;
            padding: 16px;
            border-radius: 8px;
            background-color: white;
            font-size: 16px;
            line-height: 1.6;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease-in-out;
        }
        .editor:focus-within {
            border-color: #4299e1;
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.5);
        }
        .highlight {
            cursor: pointer;
            padding: 0.5px 0;
            margin: -0.5px 0;
            border-radius: 3px;
            transition: background-color 0.2s ease-in-out;
        }
        .highlight-spell {
            background-color: rgba(255, 0, 0, 0.18);
            border-bottom: 2px solid rgba(255, 0, 0, 0.5);
        }
        .highlight-spell:hover {
            background-color: rgba(255, 0, 0, 0.28);
        }
        .highlight-grammar {
            background-color: rgba(0, 0, 255, 0.15);
            border-bottom: 2px solid rgba(0, 0, 255, 0.5);
        }
        .highlight-grammar:hover {
            background-color: rgba(0, 0, 255, 0.25);
        }
        .highlight-style {
            background-color: rgba(0, 128, 0, 0.13);
            border-bottom: 2px solid rgba(0, 128, 0, 0.5);
        }
        .highlight-style:hover {
            background-color: rgba(0, 128, 0, 0.22);
        }
        .tooltip {
            position: absolute;
            background-color: white;
            border: 1px solid #cbd5e0;
            border-radius: 8px;
            padding: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            z-index: 100;
            width: 280px;
            font-size: 14px;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.2s, visibility 0.2s, transform 0.2s;
            transform: translateY(10px);
        }
        .tooltip.active {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        .tooltip-header {
            font-weight: 600;
            margin-bottom: 8px;
            color: #2d3748;
        }
        .tooltip-body {
            margin-bottom: 12px;
            color: #4a5568;
        }
        .tooltip-suggestion {
            font-weight: 500;
            color: #2b6cb0;
        }
        .tooltip-buttons button {
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 13px;
            font-weight: 500;
            transition: background-color 0.2s;
            cursor: pointer;
        }
        .tooltip-buttons .correct-btn {
            background-color: #48bb78;
            color: white;
            margin-right: 8px;
        }
        .tooltip-buttons .correct-btn:hover {
            background-color: #38a169;
        }
        .tooltip-buttons .dismiss-btn {
            background-color: #e2e8f0;
            color: #4a5568;
        }
        .tooltip-buttons .dismiss-btn:hover {
            background-color: #cbd5e0;
        }
        .file-input-wrapper {
            position: relative;
            overflow: hidden;
            display: inline-block;
            padding: 10px 16px;
            font-size: 14px;
            font-weight: 500;
            color: white;
            background-color: #4299e1;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .file-input-wrapper:hover {
            background-color: #3182ce;
        }
        .file-input-wrapper input[type="file"] {
            position: absolute;
            left: 0;
            top: 0;
            opacity: 0;
            font-size: 100px;
            cursor: pointer;
        }
        .message-box {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background-color: #48bb78;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s, visibility 0.3s, top 0.3s;
        }
        .message-box.show {
            opacity: 1;
            visibility: visible;
            top: 40px;
        }
    </style>
</head>
<body class="p-4 md:p-8">
    <div class="max-w-4xl mx-auto bg-white p-6 md:p-8 rounded-xl shadow-2xl">
        <header class="mb-8 text-center">
            <h1 class="text-3xl md:text-4xl font-bold text-gray-800">Türkçe Dilbilgisi ve Stil Kontrolü</h1>
            <p class="text-gray-600 mt-2 text-sm md:text-base">Metninizi anında analiz edin ve geliştirin.</p>
        </header>
        <div class="control-panel grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
            <div>
                <label for="tone-select" class="block text-sm mb-1">Ton Seçimi:</label>
                <select id="tone-select" class="w-full">
                    <option value="casual">Günlük</option>
                    <option value="formal" selected>Resmi</option>
                    <option value="academic">Akademik</option>
                    <option value="creative">Yaratıcı</option>
                </select>
            </div>
            <div>
                <label for="strictness-select" class="block text-sm mb-1">Katılık Düzeyi:</label>
                <select id="strictness-select" class="w-full">
                    <option value="low">Düşük</option>
                    <option value="medium" selected>Orta</option>
                    <option value="high">Yüksek</option>
                </select>
            </div>
            <div class="flex items-end">
                <div class="file-input-wrapper">
                    <span>Dosya Yükle (.txt, .docx)</span>
                    <input type="file" id="file-upload" accept=".txt,.docx">
                </div>
            </div>
        </div>
        <div id="editor" class="editor" contenteditable="true" spellcheck="false" placeholder="Türkçe metninizi buraya yazın veya yapıştırın..."></div>
        <div class="text-right mt-2">
            <span id="char-count" class="text-sm text-gray-500">0 karakter</span>
        </div>
        <div class="mt-6 flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0 sm:space-x-4">
            <button id="analyze-button" class="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg shadow-md transition duration-150 ease-in-out transform hover:scale-105">
                Metni Analiz Et
            </button>
            <button id="clear-button" class="w-full sm:w-auto bg-gray-500 hover:bg-gray-600 text-white font-semibold py-3 px-6 rounded-lg shadow-md transition duration-150 ease-in-out">
                Temizle
            </button>
        </div>
        <div id="tooltip" class="tooltip">
            <div id="tooltip-header" class="tooltip-header">Öneri Başlığı</div>
            <div class="tooltip-body" id="tooltip-body">
                Bu kısımda hata açıklaması ve <span id="tooltip-suggestion" class="tooltip-suggestion">önerilen düzeltme</span> yer alacak.
            </div>
            <div class="tooltip-buttons">
                <button id="correct-button" class="correct-btn">Düzelt</button>
                <button id="dismiss-button" class="dismiss-btn">Yoksay</button>
            </div>
        </div>
    </div>
    <div id="message-box" class="message-box">Mesajınız burada görünecek.</div>
    <script>
        const editor = document.getElementById('editor');
        const analyzeButton = document.getElementById('analyze-button');
        const clearButton = document.getElementById('clear-button');
        const tooltip = document.getElementById('tooltip');
        const tooltipHeader = document.getElementById('tooltip-header');
        const tooltipBody = document.getElementById('tooltip-body');
        const tooltipSuggestion = document.getElementById('tooltip-suggestion');
        const correctButton = document.getElementById('correct-button');
        const dismissButton = document.getElementById('dismiss-button');
        const charCount = document.getElementById('char-count');
        const fileUploadInput = document.getElementById('file-upload');
        const messageBox = document.getElementById('message-box');
        let currentHighlight = null;
        function showMessage(message, type = 'success', duration = 3000) {
            messageBox.textContent = message;
            messageBox.className = 'message-box';
            if (type === 'success') {
                messageBox.classList.add('bg-green-500');
            } else if (type === 'error') {
                messageBox.classList.add('bg-red-500');
            } else {
                messageBox.classList.add('bg-blue-500');
            }
            messageBox.classList.add('show');
            setTimeout(() => {
                messageBox.classList.remove('show');
            }, duration);
        }
        analyzeButton.addEventListener('click', () => {
            const text = editor.textContent;
            if (!text.trim()) {
                showMessage('Lütfen analiz edilecek bir metin girin.', 'error');
                return;
            }
            clearHighlights();
            const newHtml = simulateAnalysis(text);
            editor.innerHTML = newHtml;
            addHighlightListeners();
            showMessage('Metin analizi tamamlandı (simülasyon).', 'success');
            updateCharCount();
        });
        clearButton.addEventListener('click', () => {
            editor.innerHTML = '';
            hideTooltip();
            updateCharCount();
            showMessage('Editör temizlendi.', 'info');
        });
        editor.addEventListener('input', updateCharCount);
        function updateCharCount() {
            const text = editor.textContent || "";
            charCount.textContent = `${text.length} karakter`;
        }
        updateCharCount();
        fileUploadInput.addEventListener('change', handleFileUpload);
        function handleFileUpload(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    if (file.name.endsWith('.txt')) {
                        editor.textContent = e.target.result;
                        updateCharCount();
                        showMessage(`${file.name} başarıyla yüklendi.`, 'success');
                    } else if (file.name.endsWith('.docx')) {
                        editor.textContent = `"${file.name}" yüklendi (DOCX içeriği gösterimi bu prototipte desteklenmiyor).`;
                        updateCharCount();
                        showMessage(`${file.name} yüklendi (içerik gösterimi yok).`, 'info');
                    } else {
                        showMessage('Desteklenmeyen dosya türü. Lütfen .txt veya .docx seçin.', 'error');
                    }
                    event.target.value = null;
                };
                if (file.name.endsWith('.txt')) {
                    reader.readAsText(file);
                } else if (file.name.endsWith('.docx')) {
                    reader.readAsText(file);
                }
            }
        }
        function simulateAnalysis(text) {
            let newText = text;
            const issues = [];
            if (text.includes("yanlıs")) issues.push({ word: "yanlıs", type: "spell", suggestion: "yanlış", explanation: "Yazım hatası: 'ş' harfi eksik." });
            if (text.includes("geliyom")) issues.push({ word: "geliyom", type: "grammar", suggestion: "geliyorum", explanation: "Fiil çekimi hatası: Şimdiki zaman birinci tekil şahıs eki '-yorum' olmalı." });
            if (text.includes("cok")) issues.push({ word: "cok", type: "spell", suggestion: "çok", explanation: "Yazım hatası: 'ç' harfi kullanılmalı." });
            if (text.includes("Kitap okumak iyidir")) issues.push({ word: "Kitap okumak iyidir", type: "style", suggestion: "Kitap okumak faydalıdır", explanation: "Daha etkili kelime seçimi." });
            if (text.includes("Bu metin hatalar iceriyor")) issues.push({ word: "iceriyor", type: "spell", suggestion: "içeriyor", explanation: "Yazım hatası: 'ç' harfi kullanılmalı." });
            issues.sort((a, b) => b.word.length - a.word.length);
            issues.forEach(issue => {
                const regex = new RegExp(`\\b${issue.word}\\b`, 'gi');
                newText = newText.replace(regex, (match) =>
                    `<span class="highlight highlight-${issue.type}" data-suggestion="${issue.suggestion}" data-explanation="${issue.explanation}" data-original="${match}">${match}</span>`
                );
            });
            return newText;
        }
        function clearHighlights() {
            const highlights = editor.querySelectorAll('.highlight');
            highlights.forEach(span => {
                span.outerHTML = span.textContent;
            });
            editor.normalize();
        }
        function showTooltip(event) {
            const target = event.target;
            if (!target.classList.contains('highlight')) return;
            currentHighlight = target;
            const suggestion = target.dataset.suggestion;
            const explanation = target.dataset.explanation;
            const errorType = target.classList.contains('highlight-spell') ? 'Yazım Hatası' :
                              target.classList.contains('highlight-grammar') ? 'Dilbilgisi Hatası' : 'Stil Önerisi';
            tooltipHeader.textContent = errorType;
            tooltipSuggestion.textContent = suggestion;
            tooltipBody.innerHTML = `${explanation}<br>Öneri: <strong class="tooltip-suggestion">${suggestion}</strong>`;
            const rect = target.getBoundingClientRect();
            const editorRect = editor.getBoundingClientRect();
            tooltip.style.left = `${rect.left - editorRect.left}px`;
            tooltip.style.top = `${rect.bottom - editorRect.top + 5}px`;
            tooltip.classList.add('active');
        }
        function hideTooltip() {
            tooltip.classList.remove('active');
            currentHighlight = null;
        }
        function addHighlightListeners() {
            const highlights = editor.querySelectorAll('.highlight');
            highlights.forEach(span => {
                span.addEventListener('click', showTooltip);
            });
        }
        addHighlightListeners();
        correctButton.addEventListener('click', () => {
            if (currentHighlight) {
                const suggestion = currentHighlight.dataset.suggestion;
                currentHighlight.textContent = suggestion;
                currentHighlight.classList.remove('highlight', 'highlight-spell', 'highlight-grammar', 'highlight-style');
                currentHighlight.removeAttribute('data-suggestion');
                currentHighlight.removeAttribute('data-explanation');
                currentHighlight.removeAttribute('data-original');
                currentHighlight.removeEventListener('click', showTooltip);
                editor.normalize();
            }
            hideTooltip();
        });
        dismissButton.addEventListener('click', () => {
            if (currentHighlight) {
                currentHighlight.classList.remove('highlight', 'highlight-spell', 'highlight-grammar', 'highlight-style');
                currentHighlight.removeEventListener('click', showTooltip);
                currentHighlight.outerHTML = currentHighlight.textContent;
                editor.normalize();
            }
            hideTooltip();
        });
        document.addEventListener('click', (event) => {
            if (!tooltip.contains(event.target) && event.target !== currentHighlight && !event.target.classList.contains('highlight')) {
                hideTooltip();
            }
        });
        editor.innerHTML = `Bu <span class="highlight highlight-spell" data-suggestion="yanlış" data-explanation="Yazım hatası: 'ş' harfi eksik." data-original="yanlıs">yanlıs</span> bir cümledir. Ben okula <span class="highlight highlight-grammar" data-suggestion="gidiyorum" data-explanation="Fiil çekimi hatası: Şimdiki zaman birinci tekil şahıs eki '-yorum' olmalı." data-original="gidiyom">gidiyom</span>. Bu <span class="highlight highlight-spell" data-suggestion="çok" data-explanation="Yazım hatası: 'ç' harfi kullanılmalı." data-original="cok">cok</span> iyi. <span class="highlight highlight-style" data-suggestion="Kitap okumak faydalıdır." data-explanation="Daha etkili kelime seçimi." data-original="Kitap okumak iyidir">Kitap okumak iyidir</span>. Bu metin hatalar <span class="highlight highlight-spell" data-suggestion="içeriyor" data-explanation="Yazım hatası: 'ç' harfi kullanılmalı." data-original="iceriyor">iceriyor</span>.`;
        addHighlightListeners();
        updateCharCount();
    </script>
</body>
</html>
