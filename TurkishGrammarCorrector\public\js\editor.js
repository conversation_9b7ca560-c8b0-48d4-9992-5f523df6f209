/**
 * TürkçeDüzelt - Turkish Grammar Correction Assistant
 * Editor functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    // Editor elements
    const editor = document.getElementById('editor');
    const wordCount = document.getElementById('word-count');
    const charCount = document.getElementById('char-count');
    const errorCount = document.getElementById('error-count');
    const writingModeSelect = document.getElementById('writing-mode');
    
    // Toolbar buttons
    const toolbarButtons = document.querySelectorAll('.toolbar button[data-command]');
    
    // Initialize editor
    initEditor();
    
    /**
     * Initialize the editor and set up event listeners
     */
    function initEditor() {
        // Set focus to editor
        editor.focus();
        
        // Update statistics on initial load
        updateStatistics();
        
        // Add event listeners
        editor.addEventListener('input', updateStatistics);
        editor.addEventListener('keydown', handleTabKey);
        
        // Set up toolbar buttons
        toolbarButtons.forEach(button => {
            button.addEventListener('click', () => {
                const command = button.getAttribute('data-command');
                if (command) {
                    document.execCommand(command, false, null);
                    editor.focus();
                    
                    // Toggle active state for formatting buttons
                    if (['bold', 'italic', 'underline'].includes(command)) {
                        button.classList.toggle('active');
                    }
                }
            });
        });
        
        // Writing mode change
        writingModeSelect.addEventListener('change', function() {
            // This would trigger different grammar checking rules based on the selected mode
            console.log(`Writing mode changed to: ${this.value}`);
            triggerRealtimeGrammarCheck();
        });
        
        // --- Real-time grammar check (debounced) ---
        let grammarCheckTimeout = null;
        editor.addEventListener('input', function() {
            if (grammarCheckTimeout) clearTimeout(grammarCheckTimeout);
            grammarCheckTimeout = setTimeout(triggerRealtimeGrammarCheck, 1200); // 1.2s debounce
        });
    }
    
    // --- Real-time grammar check trigger ---
    function triggerRealtimeGrammarCheck() {
        if (window.grammarCheckerModule && typeof window.grammarCheckerModule.checkGrammar === 'function') {
            window.grammarCheckerModule.checkGrammar();
        }
    }
    
    /**
     * Update word count, character count, and other statistics
     */
    function updateStatistics() {
        const text = editor.innerText || '';
        
        // Count words (handling Turkish characters)
        const words = text.trim().split(/\s+/).filter(word => word.length > 0);
        wordCount.textContent = words.length;
        
        // Count characters (including Turkish special characters)
        charCount.textContent = text.length;
        
        // Error count will be updated by the grammar checker
    }
    
    /**
     * Handle tab key in the editor
     */
    function handleTabKey(e) {
        if (e.key === 'Tab') {
            e.preventDefault();
            document.execCommand('insertHTML', false, '&nbsp;&nbsp;&nbsp;&nbsp;');
        }
    }
    
    // File operations
    const newDocumentBtn = document.getElementById('new-document');
    const uploadDocumentBtn = document.getElementById('upload-document');
    const fileInput = document.getElementById('file-input');
    const downloadDocumentBtn = document.getElementById('download-document');
    
    // New document
    newDocumentBtn.addEventListener('click', function() {
        if (confirm('Are you sure you want to create a new document? Any unsaved changes will be lost.')) {
            editor.innerHTML = '<p>Buraya metninizi yazın veya yapıştırın.</p>';
            updateStatistics();
        }
    });
    
    // Upload document
    uploadDocumentBtn.addEventListener('click', function() {
        fileInput.click();
    });
    
    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (!file) return;
        
        const reader = new FileReader();
        
        reader.onload = function(e) {
            const content = e.target.result;
            
            // Handle different file types
            if (file.name.endsWith('.txt')) {
                // For text files, split by newlines and wrap in paragraphs
                const paragraphs = content.split('\n').map(p => `<p>${p}</p>`).join('');
                editor.innerHTML = paragraphs || '<p></p>';
            } else if (file.name.endsWith('.docx')) {
                // For .docx files, we'd need a library like mammoth.js
                // This is a simplified placeholder
                editor.innerHTML = '<p>DOCX dosyası yüklendi. (Bu demo sürümünde DOCX içeriği gösterilemiyor.)</p>';
            }
            
            updateStatistics();
        };
        
        if (file.name.endsWith('.txt')) {
            reader.readAsText(file);
        } else {
            // For other file types, we'd need additional libraries
            alert('Bu dosya türü şu anda desteklenmiyor.');
        }
        
        // Reset file input
        fileInput.value = '';
    });
    
    // Download document
    downloadDocumentBtn.addEventListener('click', function() {
        const content = editor.innerHTML;
        const blob = new Blob([content], {type: 'text/html'});
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = 'turkce-duzelt-dokuman.html';
        document.body.appendChild(a);
        a.click();
        
        // Clean up
        setTimeout(() => {
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }, 0);
    });
    
    // Expose functions for other modules
    window.editorModule = {
        getContent: function() {
            return editor.innerHTML;
        },
        setContent: function(html) {
            editor.innerHTML = html;
            updateStatistics();
        },
        highlightError: function(startNode, startOffset, endNode, endOffset, errorType) {
            const range = document.createRange();
            range.setStart(startNode, startOffset);
            range.setEnd(endNode, endOffset);
            
            const span = document.createElement('span');
            span.className = errorType + '-error';
            span.dataset.errorType = errorType;
            
            range.surroundContents(span);
            
            // Add click event to show error details
            span.addEventListener('click', function() {
                // This will be implemented in grammar-checker.js
                if (window.grammarCheckerModule) {
                    window.grammarCheckerModule.showErrorDetails(this);
                }
            });
        },
        removeHighlights: function() {
            const highlights = editor.querySelectorAll('.grammar-error, .spelling-error, .style-suggestion');
            highlights.forEach(highlight => {
                const parent = highlight.parentNode;
                while (highlight.firstChild) {
                    parent.insertBefore(highlight.firstChild, highlight);
                }
                parent.removeChild(highlight);
            });
        }
    };
});
