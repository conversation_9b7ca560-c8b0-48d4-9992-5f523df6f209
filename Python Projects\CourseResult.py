#Part 2: my_result ()#my_result () is a function that takes an input from a user and returns some performance records in CS 1101#Upon calling the function, python interpreter prompts you to input your total scores,it returns your grade, credits etc.
def my_result(score): 
    a = float(score)    
    cred = 3    
    if a >= 98 and a <=100:
        final_grade = 'A+ Excellent'  
        point_value1 = 4.00     
        print('\nCourse: CS 1101 \n\nCredits: ' + str(cred) + '\n\nPoint Value: ' + str(point_value1) + '\n\nGrade Point: ' + str((int(cred)*float(point_value1))) + '\n\nFinal Grade: ' + final_grade)    
    elif a >= 93 and a < 98:   
        final_grade = 'A Excellent'   
        point_value2 = 4.00     
        print('\nCourse: CS 1101 \n\nCredits: ' + str(cred) + '\n\nPoint Value: ' + str(point_value2) + '\n\nGrade Point: ' + str((int(cred)*float(point_value2))) + '\n\nFinal Grade: ' + final_grade)  
    elif a >= 90 and a < 93: 
        final_grade = 'A- Excellent' 
        point_value3 = 3.67     
        print('\nCourse: CS 1101 \n\nCredits: ' + str(cred) + '\n\nPoint Value: ' + str(point_value3) + '\n\nGrade Point: ' + str((int(cred)*float(point_value3))) + '\n\nFinal Grade: ' + final_grade)  
    elif a >= 88 and a < 90:   
        final_grade = 'B+ Above Average'
        point_value4 = 3.33    
        print('\nCourse: CS 1101 \n\nCredits: ' + str(cred) + '\n\nPoint Value: ' + str(point_value4) + '\n\nGrade Point: ' + str((int(cred)*float(point_value4))) + '\n\nFinal Grade: ' + final_grade)  
    elif a >= 83 and a < 88:  
        final_grade = 'B Above Average' 
        point_value5 = 3.00  
        print('\nCourse: CS 1101 \n\nCredits: ' + str(cred) + '\n\nPoint Value: ' + str(point_value5) + '\n\nGrade Point: ' + str((int(cred)*float(point_value5))) + '\n\nFinal Grade: ' + final_grade) 
    elif a >= 80 and a < 83: 
        final_grade = 'B- Above Average'  
        point_value6 = 2.67  
        print('\nCourse: CS 1101 \n\nCredits: ' + str(cred) + '\n\nPoint Value: ' + str(point_value6) + '\n\nGrade Point: ' + str((int(cred)*float(point_value6))) + '\n\nFinal Grade: ' + final_grade)  
    elif a >= 78 and a < 98: 
        final_grade = 'C+ Average' 
        point_value7 = 2.33    
        print('\nCourse: CS 1101 \n\nCredits: ' + str(cred) + '\n\nPoint Value: ' + str(point_value7) + '\n\nGrade Point: ' + str((int(cred)*float(point_value7))) + '\n\nFinal Grade: ' + final_grade)
    elif a >= 73 and a < 98:
        final_grade = 'C Average'   
        point_value8 = 2.00    
        print('\nCourse: CS 1101 \n\nCredits: ' + str(cred) + '\n\nPoint Value: ' + str(point_value8) + '\n\nGrade Point: ' + str((int(cred)*float(point_value8))) + '\n\nFinal Grade: ' + final_grade) 
    elif a >= 70 and a < 98:
        final_grade = 'C- Average' 
        point_value9 = 1.67  
        print('Course: CS 1101 \nCredits: ' + str(cred) + '\n\nPoint Value: ' + str(point_value9) + '\n\nGrade Point: ' + str((int(cred)*float(point_value9))) + '\n\nFinal Grade: ' + final_grade) 
    elif a >= 68 and a < 98:  
        final_grade = 'D+ Poor'  
        point_value10 = 1.33   
        print('\nCourse: CS 1101 \n\nCredits: ' + str(cred) + '\n\nPoint Value: ' + str(point_value10) + '\n\nGrade Point: ' + str((int(cred)*float(point_value10))) + '\n\nFinal Grade: ' + final_grade)      
        print('')    
    elif a >= 63 and a < 98: 
        final_grade = 'D Poor' 
        point_value11 = 1.00  
        print('\nCourse: CS 1101 \n\nCredits: ' + str(cred) + '\n\nPoint Value: ' + str(point_value11) + '\n\nGrade Point: ' + str((int(cred)*float(point_value11))) + '\n\nFinal Grade: ' + final_grade)
    elif a >= 60 and a < 98:  
        final_grade = 'D- Poor'  
        point_value12 = 0.67   
        print('\nCourse: CS 1101 \n\nCredits: ' + str(cred) + '\n\nPoint Value: ' + str(point_value12) + '\n\nGrade Point: ' + str((int(cred)*float(point_value12))) + '\n\nFinal Grade: ' + final_grade) 
    elif a < 60: 
        final_grade = 'F Fail'  
        point_value13 = 0.00  
        print('\nCourse: CS 1101 \n\nCredits: ' + str(cred) + '\n\nPoint Value: ' + str(point_value13) + '\n\nGrade Point: ' + str((int(cred)*float(point_value13))) + '\n\nFinal Grade: ' + final_grade + '\n\n')


print('') 
my_result(56)
my_result(98)
print('')
print('')
my_result(float(input("Score = ")))